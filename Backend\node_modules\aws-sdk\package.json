{"name": "aws-sdk", "description": "AWS SDK for JavaScript", "version": "2.1692.0", "author": {"name": "Amazon Web Services", "email": "", "url": "https://aws.amazon.com/"}, "config": {"test_args": "test test/json test/model test/protocol test/query test/services test/signers test/xml test/s3 test/cloudfront test/dynamodb test/polly test/rds test/publisher test/event-stream test/endpoint"}, "homepage": "https://github.com/aws/aws-sdk-js", "contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "devDependencies": {"@types/node": "6.0.92", "browserify": "13.1.0", "chai": "^3.0", "codecov": "^3.8.2", "coffeeify": "*", "coffeescript": "^1.12.7", "cucumber": "0.5.x", "eslint": "^5.8.0", "hash-test-vectors": "^1.3.2", "insert-module-globals": "^7.0.0", "istanbul": "*", "jasmine": "^2.5.3", "jasmine-core": "^2.5.2", "json-loader": "^0.5.4", "karma": "^4.1.0", "karma-chrome-launcher": "2.2.0", "karma-jasmine": "^1.1.0", "mocha": "^3.0.0", "repl.history": "*", "semver": "*", "typescript": "2.0.8", "uglify-js": "2.x", "webpack": "^1.15.0"}, "dependencies": {"buffer": "4.9.2", "events": "1.1.1", "ieee754": "1.1.13", "jmespath": "0.16.0", "querystring": "0.2.0", "sax": "1.2.1", "url": "0.10.3", "util": "^0.12.4", "uuid": "8.0.0", "xml2js": "0.6.2"}, "main": "lib/aws.js", "browser": {"lib/aws.js": "./lib/browser.js", "fs": false, "./global.js": "./browser.js", "./lib/node_loader.js": "./lib/browser_loader.js"}, "browserify": {"transform": "./dist-tools/transform.js"}, "react-native": {"fs": "./lib/empty.js", "./lib/node_loader.js": "./lib/react-native-loader.js", "./lib/browser_loader.js": "./lib/react-native-loader.js", "./lib/core.js": "./dist/aws-sdk-core-react-native.js", "xml2js": "./dist/xml2js.js"}, "directories": {"lib": "lib"}, "types": "index.d.ts", "typings": "index.d.ts", "engines": {"node": ">= 10.0.0"}, "repository": {"type": "git", "url": "git://github.com/aws/aws-sdk-js"}, "bugs": {"url": "https://github.com/aws/aws-sdk-js/issues", "mail": ""}, "license": "Apache-2.0", "keywords": ["api", "amazon", "aws", "ec2", "simpledb", "s3", "sqs", "ses", "sns", "route53", "rds", "elasticache", "cloudfront", "fps", "cloudformation", "cloudwatch", "dynamodb", "iam", "swf", "autoscaling", "cloudsearch", "elb", "loadbalancing", "emr", "mapreduce", "importexport", "storagegateway", "workflow", "ebs", "vpc", "beanstalk", "glacier", "kinesis", "cloudtrail", "waf"], "scripts": {"test": "node ./scripts/composite-test.js", "unit": "mocha -- $npm_package_config_test_args", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha --reporter=lcovonly -- $npm_package_config_test_args", "browsertest": "rake browser:test && karma start", "buildertest": "mocha --require coffeescript/register -s 1000 -t 10000 dist-tools/test/*.coffee", "integration": "cucumber.js", "lint": "eslint lib test dist-tools/*.js", "console": "./scripts/console", "testfiles": "istanbul `[ $COVERAGE ] && echo 'cover _mocha' || echo 'test mocha'`", "tstest": "npm -s run-script build-typings && tsc -p ./ts", "build-typings": "node ./scripts/typings-generator.js", "add-change": "node ./scripts/changelog/add-change.js", "build-react-native-deps": "webpack --config dist-tools/webpack.config.rn-dep.js", "build-react-native-core": "webpack --config dist-tools/webpack.config.rn-core.js", "build-react-native-dist": "webpack --config dist-tools/webpack.config.rn.js", "build-react-native": "npm -s run-script build-react-native-deps && npm -s run-script build-react-native-core && npm -s run-script build-react-native-dist", "react-native-test": "npm -s run-script build-react-native && rake reactnative:test && karma start", "region-check": "node ./scripts/region-checker/index.js", "translate-api-test": "mocha scripts/lib/translate-api.spec.js", "typings-generator-test": "mocha scripts/lib/prune-shapes.spec.js", "helper-test": "mocha scripts/lib/test-helper.spec.js", "csm-functional-test": "mocha test/publisher/functional_test", "postinstall": "node scripts/warn-maintenance-mode.js"}}