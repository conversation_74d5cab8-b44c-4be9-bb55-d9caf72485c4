export import ACM = require('./acm');
export import APIGateway = require('./apigateway');
export import ApplicationAutoScaling = require('./applicationautoscaling');
export import AutoScaling = require('./autoscaling');
export import CloudFormation = require('./cloudformation');
export import CloudFront = require('./cloudfront');
export import CloudHSM = require('./cloudhsm');
export import CloudTrail = require('./cloudtrail');
export import CloudWatch = require('./cloudwatch');
export import CloudWatchEvents = require('./cloudwatchevents');
export import CloudWatchLogs = require('./cloudwatchlogs');
export import CodeBuild = require('./codebuild');
export import CodeCommit = require('./codecommit');
export import CodeDeploy = require('./codedeploy');
export import CodePipeline = require('./codepipeline');
export import CognitoIdentity = require('./cognitoidentity');
export import CognitoIdentityServiceProvider = require('./cognitoidentityserviceprovider');
export import CognitoSync = require('./cognitosync');
export import ConfigService = require('./configservice');
export import CUR = require('./cur');
export import DeviceFarm = require('./devicefarm');
export import DirectConnect = require('./directconnect');
export import DynamoDB = require('./dynamodb');
export import DynamoDBStreams = require('./dynamodbstreams');
export import EC2 = require('./ec2');
export import ECR = require('./ecr');
export import ECS = require('./ecs');
export import EFS = require('./efs');
export import ElastiCache = require('./elasticache');
export import ElasticBeanstalk = require('./elasticbeanstalk');
export import ELB = require('./elb');
export import ELBv2 = require('./elbv2');
export import EMR = require('./emr');
export import ElasticTranscoder = require('./elastictranscoder');
export import Firehose = require('./firehose');
export import GameLift = require('./gamelift');
export import IAM = require('./iam');
export import Inspector = require('./inspector');
export import Iot = require('./iot');
export import IotData = require('./iotdata');
export import Kinesis = require('./kinesis');
export import KMS = require('./kms');
export import Lambda = require('./lambda');
export import LexRuntime = require('./lexruntime');
export import MachineLearning = require('./machinelearning');
export import MarketplaceCommerceAnalytics = require('./marketplacecommerceanalytics');
export import MTurk = require('./mturk');
export import MobileAnalytics = require('./mobileanalytics');
export import OpsWorks = require('./opsworks');
export import Polly = require('./polly');
export import RDS = require('./rds');
export import Redshift = require('./redshift');
export import Rekognition = require('./rekognition');
export import Route53 = require('./route53');
export import Route53Domains = require('./route53domains');
export import S3 = require('./s3');
export import ServiceCatalog = require('./servicecatalog');
export import SES = require('./ses');
export import SNS = require('./sns');
export import SQS = require('./sqs');
export import SSM = require('./ssm');
export import StorageGateway = require('./storagegateway');
export import STS = require('./sts');
export import XRay = require('./xray');
export import WAF = require('./waf');
export import WorkDocs = require('./workdocs');
export import LexModelBuildingService = require('./lexmodelbuildingservice');
export import Athena = require('./athena');
export import CloudHSMV2 = require('./cloudhsmv2');
export import Pricing = require('./pricing');
export import CostExplorer = require('./costexplorer');
export import MediaStoreData = require('./mediastoredata');
export import Comprehend = require('./comprehend');
export import KinesisVideoArchivedMedia = require('./kinesisvideoarchivedmedia');
export import KinesisVideoMedia = require('./kinesisvideomedia');
export import KinesisVideo = require('./kinesisvideo');
export import Translate = require('./translate');
export import ResourceGroups = require('./resourcegroups');
export import Connect = require('./connect');
export import SecretsManager = require('./secretsmanager');
export import IoTAnalytics = require('./iotanalytics');
export import ComprehendMedical = require('./comprehendmedical');
export import Personalize = require('./personalize');
export import PersonalizeEvents = require('./personalizeevents');
export import PersonalizeRuntime = require('./personalizeruntime');
export import ForecastService = require('./forecastservice');
export import ForecastQueryService = require('./forecastqueryservice');
export import MarketplaceCatalog = require('./marketplacecatalog');
export import KinesisVideoSignalingChannels = require('./kinesisvideosignalingchannels');
export import Amp = require('./amp');
export import Location = require('./location');
export import LexRuntimeV2 = require('./lexruntimev2');
