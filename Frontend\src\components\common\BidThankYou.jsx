import React from 'react';
import './../../styles/BidThankYou.css'; // Adjusted path
import mastercardLogo from './../../assets/images/mastercard-logo.png'; // Adjusted path
import itemImage from './../../assets/images/herosideimg.svg'; // Placeholder, replace with actual image if available
import successIcon from './../../assets/images/thankyou.svg'; // Assuming this is the checkmark icon

const BidThankYou = () => {
  return (
    <div className="bid-thank-you-page max-container">
      <div className="bid-thank-you-header">
        <img src={successIcon} alt="Success" className="bid-thank-you-header-icon" />
        <h1 className="bid-thank-you-title">Your bid is submitted successfully!</h1>
        <p className="bid-thank-you-subtitle">We will update you for the bid status soon via Email or SMS.</p>
      </div>

      <div className="bid-thank-you-card">
        <div className="bid-thank-you-section bid-information-section">
          <h2 className="bid-thank-you-section-title">Bid Information</h2>
          <div className="bid-thank-you-grid bid-info-grid">
            <div className="bid-thank-you-grid-item">
              <span className="bid-thank-you-label">Bid Id:</span>
              <span className="bid-thank-you-value">#12345678</span>
            </div>
            <div className="bid-thank-you-grid-item">
              <span className="bid-thank-you-label">Price:</span>
              <span className="bid-thank-you-value">$22.00</span>
            </div>
            {/* Vertical Divider for Bid Info */}
            <div className="bid-thank-you-vertical-divider bid-info-divider"></div>
            <div className="bid-thank-you-grid-item">
              <span className="bid-thank-you-label">Date:</span>
              <span className="bid-thank-you-value">20 May 2025 | 4:50PM</span>
            </div>
            <div className="bid-thank-you-grid-item">
              <span className="bid-thank-you-label">Bid Amount:</span>
              <span className="bid-thank-you-value">$19.00</span>
            </div>
          </div>
        </div>

        <hr className="bid-thank-you-divider" />

        <div className="bid-thank-you-section customer-payment-section">
          <div className="customer-payment-flex-container">
            <div className="customer-details-container">
              <h2 className="bid-thank-you-section-title">Customer Details</h2>
              <div className="bid-thank-you-customer-details">
                <div>
                  <span className="bid-thank-you-label">Name:</span>
                  <span className="bid-thank-you-value">John Smith</span>
                </div>
                <div>
                  <span className="bid-thank-you-label">Email Address:</span>
                  <span className="bid-thank-you-value"><EMAIL></span>
                </div>
                <div>
                  <span className="bid-thank-you-label">Phone Number:</span>
                  <span className="bid-thank-you-value">************</span>
                </div>
              </div>
            </div>
            {/* Vertical Divider for Customer/Payment */}
            <div className="bid-thank-you-vertical-divider customer-payment-divider"></div>
            <div className="payment-details-container">
              <h2 className="bid-thank-you-section-title">Payment Details</h2>
              <div className="bid-thank-you-payment-details">
                <img src={mastercardLogo} alt="Mastercard" className="bid-thank-you-payment-icon" />
                <span className="bid-thank-you-value">**** **** **** 1234</span>
              </div>
            </div>
          </div>
        </div>

        <hr className="bid-thank-you-divider" />

        <div className="bid-thank-you-section">
          <h2 className="bid-thank-you-section-title">Item Info</h2>
          <div className="bid-thank-you-item-info">
            <img src={itemImage} alt="Item" className="bid-thank-you-item-image" />
            <div className="bid-thank-you-item-details">
              <p className="bid-thank-you-item-title">Frank Martin - Drills and Coaching Philosophies to Developing Toughness In Your Players</p>
              <p className="bid-thank-you-item-author">By Basketball Coaches Clinic</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bid-thank-you-actions">
        <button type="button" className="btn btn-outline bid-thank-you-btn-outline">Go To My Bid Page</button>
        <button type="button" className="btn btn-primary bid-thank-you-btn-primary">Go To Homepage</button>
      </div>
    </div>
  );
};

export default BidThankYou;