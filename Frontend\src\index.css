/* Base Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  font-family: "Poppins", sans-serif;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
}
p {
  margin: 0;
}
a {
  color: var(--btn-color);
  font-size: var(--basefont);
  text-decoration: none;
  transition: color 0.3s ease;
}

/* Root Variables */
:root {
  /* Colors */
  --primary-color: #960d12;

  --primary-light-color: #fdf5f0;
  --btn-color: #ee3425;
  --secondary-color: #163351;
  --text-color: #1c1c1c;
  --dark-gray: #666666;
  --light-gray: #d9d9d9;
  --bg-gray: #f5f5f5;
  --bg-blue: #f1f5f9;
  --white: #ffffff;
  --black: #000000;
  --error-color: #dc3545;
  --primary-rgb: 150, 13, 18;
  /* <PERSON>ont Sizes */
  --heading1: 42px;
  --heading2: 40px;
  --heading3: 32px;
  --heading4: 28px;
  --heading5: 20px;
  --heading6: 18px;
  --basefont: 16px;
  --smallfont: 14px;
  --extrasmallfont: 12px;
  /* Box Model Properties */
  --border-radius: 4px;
  --border-radius-large: 12px;
  --border-radius-medium: 8px;
  /* Spacing Variables */
  --m-5: 5px;
  --m-10: 10px;
  --m-15: 15px;
  --m-20: 20px;
  --m-30: 30px;
  --m-40: 40px;
  --m-50: 50px;
  --p-5: 5px;
  --p-10: 10px;
  --p-15: 15px;
  --p-20: 20px;
  --p-30: 30px;
  --p-40: 40px;
  --p-50: 50px;
  --gap-5: 5px;
  --gap-10: 10px;
  --gap-15: 15px;
  --gap-20: 20px;
  --gap-30: 30px;
  --gap-40: 40px;
  /* Shadows */
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.05);
  --box-shadow-dark: 0 6px 8px rgba(0, 0, 0, 0.2);
  /* Z-Index */
  --z-index-modal: 1000;
  --z-index-tooltip: 1100;
  --z-index-header: 900;
}
/* Container Styling */
.content-width {
  max-width: 1400px;
  padding: 0.5rem;
  margin: auto;
}
/* Input Styling */
input[type="checkbox"],
input[type="radio"] {
  accent-color: var(--primary-color);
  width: 18px;
  height: 18px;
}
input,
select {
  outline: none;
}
/* Text Selection */
::selection {
  color: var(--white);
  background-color: var(--primary-color);
}
/* Section Padding */
.section-padding {
  padding-top: 50px;
  padding-bottom: 50px;
}
/* Common Titles and Descriptions */
.commontitle {
  text-align: center;
  font-size: var(--heading3);
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: 10px;
}
.commondescription {
  text-align: center;
  font-size: var(--heading6);
  margin-bottom: 30px;
}
/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: var(--basefont);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  border: none;
}
.signupbtn {
  background-color: var(--btn-color);
  color: var(--white);
  border-radius: var(--border-radius-large);
}
.signupbtn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
}
.signinbtn {
  border: 1px solid var(--btn-color);
  color: var(--btn-color);
  border-radius: var(--border-radius-large);
}
.signinbtn:hover {
  color: var(--white);
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
}
.btn-primary {
  background: linear-gradient(to bottom, var(--btn-color), #fb5024);
  color: var(--white);
  padding: 14px 28px;
  border-radius: 8px;
  font-weight: 600;
  font-size: var(--basefont);
  text-align: center;
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(238, 52, 37, 0.4);
}

.btn-secondary {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  color: var(--secondary-color);
  font-weight: 600;
}

.btn-secondary:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: translateY(-2px);
}

.btn-outline {
  background-color: transparent;
  color: #ee3425;
  border: 1px solid #ee3425;
  padding: 6px 12px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-outline:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.3);
}

.btn-lg {
  padding: 16px 32px;
  font-size: var(--heading6);
  letter-spacing: 0.5px;
}

.btn-sm {
  padding: 8px 16px;
  font-size: var(--smallfont);
}

/* Layout Utility Classes */
.max-container {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  padding: 0 20px;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-10 {
  gap: 10px;
}

.gap-20 {
  gap: 20px;
}

.gap-30 {
  gap: 30px;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Spacing Utility Classes */
/* Margin Utilities */
.m-0 {
  margin: 0 !important;
}
.m-5 {
  margin: 5px;
}
.m-10 {
  margin: 10px;
}
.m-15 {
  margin: 15px;
}
.m-20 {
  margin: 20px;
}
.m-30 {
  margin: 30px;
}
.m-40 {
  margin: 40px;
}
.m-50 {
  margin: 50px;
}
.m-60 {
  margin: 60px;
}

.mt-0 {
  margin-top: 0 !important;
}
.mt-5 {
  margin-top: 5px;
}
.mt-10 {
  margin-top: 10px;
}
.mt-15 {
  margin-top: 15px;
}
.mt-20 {
  margin-top: 20px;
}
.mt-30 {
  margin-top: 30px;
}
.mt-40 {
  margin-top: 40px;
}
.mt-50 {
  margin-top: 50px;
}
.mt-60 {
  margin-top: 60px;
}

.mb-0 {
  margin-bottom: 0 !important;
}
.mb-5 {
  margin-bottom: 5px;
}
.mb-10 {
  margin-bottom: 10px;
}
.mb-15 {
  margin-bottom: 15px;
}
.mb-20 {
  margin-bottom: 20px;
}
.mb-30 {
  margin-bottom: 30px;
}
.mb-40 {
  margin-bottom: 40px;
}
.mb-50 {
  margin-bottom: 50px;
}
.mb-60 {
  margin-bottom: 60px;
}

.ml-0 {
  margin-left: 0 !important;
}
.ml-5 {
  margin-left: 5px;
}
.ml-10 {
  margin-left: 10px;
}
.ml-15 {
  margin-left: 15px;
}
.ml-20 {
  margin-left: 20px;
}
.ml-30 {
  margin-left: 30px;
}

.mr-0 {
  margin-right: 0 !important;
}
.mr-5 {
  margin-right: 5px;
}
.mr-10 {
  margin-right: 10px;
}
.mr-15 {
  margin-right: 15px;
}
.mr-20 {
  margin-right: 20px;
}
.mr-30 {
  margin-right: 30px;
}

.mx-0 {
  margin-left: 0 !important;
  margin-right: 0 !important;
}
.mx-5 {
  margin-left: 5px;
  margin-right: 5px;
}
.mx-10 {
  margin-left: 10px;
  margin-right: 10px;
}
.mx-15 {
  margin-left: 15px;
  margin-right: 15px;
}
.mx-20 {
  margin-left: 20px;
  margin-right: 20px;
}
.mx-30 {
  margin-left: 30px;
  margin-right: 30px;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
.my-5 {
  margin-top: 5px;
  margin-bottom: 5px;
}
.my-10 {
  margin-top: 10px;
  margin-bottom: 10px;
}
.my-15 {
  margin-top: 15px;
  margin-bottom: 15px;
}
.my-20 {
  margin-top: 20px;
  margin-bottom: 20px;
}
.my-30 {
  margin-top: 30px;
  margin-bottom: 30px;
}
.my-40 {
  margin-top: 40px;
  margin-bottom: 40px;
}
.my-50 {
  margin-top: 50px;
  margin-bottom: 50px;
}
.my-60 {
  margin-top: 60px;
  margin-bottom: 60px;
}

/* Padding Utilities */
.p-0 {
  padding: 0 !important;
}
.p-5 {
  padding: 5px;
}
.p-10 {
  padding: 10px;
}
.p-15 {
  padding: 15px;
}
.p-20 {
  padding: 20px;
}
.p-30 {
  padding: 30px;
}
.p-40 {
  padding: 40px;
}
.p-50 {
  padding: 50px;
}
.p-60 {
  padding: 60px;
}

.pt-0 {
  padding-top: 0 !important;
}
.pt-5 {
  padding-top: 5px;
}
.pt-10 {
  padding-top: 10px;
}
.pt-15 {
  padding-top: 15px;
}
.pt-20 {
  padding-top: 20px;
}
.pt-30 {
  padding-top: 30px;
}
.pt-40 {
  padding-top: 40px;
}
.pt-50 {
  padding-top: 50px;
}
.pt-60 {
  padding-top: 60px;
}

.pb-0 {
  padding-bottom: 0 !important;
}
.pb-5 {
  padding-bottom: 5px;
}
.pb-10 {
  padding-bottom: 10px;
}
.pb-15 {
  padding-bottom: 15px;
}
.pb-20 {
  padding-bottom: 20px;
}
.pb-30 {
  padding-bottom: 30px;
}
.pb-40 {
  padding-bottom: 40px;
}
.pb-50 {
  padding-bottom: 50px;
}
.pb-60 {
  padding-bottom: 60px;
}

.pl-0 {
  padding-left: 0 !important;
}
.pl-5 {
  padding-left: 5px;
}
.pl-10 {
  padding-left: 10px;
}
.pl-15 {
  padding-left: 15px;
}
.pl-20 {
  padding-left: 20px;
}
.pl-30 {
  padding-left: 30px;
}

.pr-0 {
  padding-right: 0 !important;
}
.pr-5 {
  padding-right: 5px;
}
.pr-10 {
  padding-right: 10px;
}
.pr-15 {
  padding-right: 15px;
}
.pr-20 {
  padding-right: 20px;
}
.pr-30 {
  padding-right: 30px;
}

.px-0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.px-5 {
  padding-left: 5px;
  padding-right: 5px;
}
.px-10 {
  padding-left: 10px;
  padding-right: 10px;
}
.px-15 {
  padding-left: 15px;
  padding-right: 15px;
}
.px-20 {
  padding-left: 20px;
  padding-right: 20px;
}
.px-30 {
  padding-left: 30px;
  padding-right: 30px;
}
.px-40 {
  padding-left: 40px;
  padding-right: 40px;
}

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.py-5 {
  padding-top: 5px;
  padding-bottom: 5px;
}
.py-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}
.py-15 {
  padding-top: 15px;
  padding-bottom: 15px;
}
.py-20 {
  padding-top: 20px;
  padding-bottom: 20px;
}
.py-30 {
  padding-top: 30px;
  padding-bottom: 30px;
}
.py-40 {
  padding-top: 40px;
  padding-bottom: 40px;
}
.py-50 {
  padding-top: 50px;
  padding-bottom: 50px;
}
.py-60 {
  padding-top: 60px;
  padding-bottom: 60px;
}

/* Section Padding Utility */
.p-section {
  padding: 60px 0;
}

/* Text Utility Classes */
.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--secondary-color);
}

.text-white {
  color: var(--white);
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}
.selectstylesnone {
  border: none;
  all: unset;

  appearance: auto;
  -webkit-appearance: auto;
  -moz-appearance: auto;
}
/* Error Message */
.error-message {
  color: red;
  font-size: var(--smallfont);
  font-weight: 400;
}
/* Responsive Media Queries */
/* Tablets (Portrait & Landscape) */
@media (max-width: 1024px) {
  :root {
    --heading1: 38px;
    --heading2: 36px;
    --heading3: 30px;
    --heading4: 26px;
    --heading5: 18px;
    --heading6: 16px;
    --basefont: 14px;
    --smallfont: 12px;
    --extrasmallfont: 10px;
  }
  .section-padding,
  .p-section {
    padding-top: 40px;
    padding-bottom: 40px;
  }
}
/* Large Smartphones (Portrait & Landscape) */
@media (max-width: 768px) {
  :root {
    --heading1: 37.5px;
    --heading2: 35px;
    --heading3: 30px;
    --heading4: 25px;
    --heading5: 18.75px;
    --heading6: 16.25px;
    --basefont: 16.25px;
    --smallfont: 13.75px;
    --extrasmallfont: 11.25px;
  }

  .section-padding,
  .p-section {
    padding-top: 30px;
    padding-bottom: 30px;
  }
  .btn-lg {
    padding: 16px 16px;
    font-size: var(--heading6);
    letter-spacing: 0.5px;
  }
  .max-container {
    padding: 0 5px;
  }
}
/* Small Smartphones (Landscape) */
@media (max-width: 640px) and (orientation: landscape) {
  .section-padding,
  .p-section {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}
/* Small Smartphones (Portrait) */
@media (max-width: 480px) and (orientation: portrait) {
  :root {
    --heading1: 27.5px; /* 22 * 1.25 */
    --heading2: 25px; /* 20 * 1.25 */
    --heading3: 22.5px; /* 18 * 1.25 */
    --heading4: 20px; /* 16 * 1.25 */
    --heading5: 16.25px; /* 13 * 1.25 */
    --heading6: 13.75px; /* 11 * 1.25 */
    --basefont: 13.75px; /* 11 * 1.25 */
    --smallfont: 12.5px; /* 10 * 1.25 */
    --extrasmallfont: 11.25px; /* 9 * 1.25 */
  }

  .section-padding,
  .p-section {
    padding-top: 25px;
    padding-bottom: 25px;
  }

  .max-container {
    padding: 0 10px;
  }
}

/* Extra Small Devices (Watches, Foldable Phones) */
@media (max-width: 320px) {
  .section-padding,
  .p-section {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
