.bid-thank-you-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: var(--p-50, 50px); /* Using fallback if --p-50 is not defined */
  padding-bottom: var(--p-50, 50px);
  
  background-color: var(--bg-gray); /* Added from previous version, ensure it's here */
  min-height: 100vh;
}

.bid-thank-you-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: var(--m-30, 30px);
}

.bid-thank-you-header-icon {
  width: 60px; /* Approximate size from image */
  height: 60px; /* Approximate size from image */
  margin-bottom: var(--m-20, 20px);
}

.bid-thank-you-title {
  font-size: var(--heading3); /* Assuming h1 maps to heading3 or similar */
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: var(--m-10, 10px);
}

.bid-thank-you-subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  max-width: 400px; /* To match the line break in the image */
}

.bid-thank-you-card {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  padding: var(--p-30, 30px);
  width: 100%;
  max-width: 800px; /* Approximate width from image */
  display: flex;
  flex-direction: column;
  gap: var(--gap-20, 20px);
}

.bid-thank-you-section {
  display: flex;
  flex-direction: column;
}

/* Styles for vertical dividers */
.bid-thank-you-vertical-divider {
  width: 1px;
  background-color: var(--light-gray);
  /* margin: 0 var(--m-20, 20px); */ /* Adjust margin as needed */
}

.bid-information-section .bid-info-grid {
  grid-template-columns: 1fr auto 1fr 1fr; /* Columns for items and divider */
  align-items: start; /* Align items to the start of the grid cell */
}

.bid-info-grid .bid-thank-you-grid-item {
   padding-right: var(--p-15, 15px); /* Add some padding to the right of items before divider */
}
.bid-info-grid .bid-thank-you-grid-item:nth-child(2) {
   padding-right: var(--p-15, 15px); /* Add some padding to the right of items before divider */
}


.bid-info-divider {
  height: 100%; /* Make divider full height of the row */
  align-self: stretch; /* Stretch divider in grid context */
}


.bid-thank-you-section-title {
  font-size: var(--heading5);
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: var(--m-15, 15px);
}

.bid-thank-you-grid {
  display: grid;
  /* grid-template-columns: repeat(2, 1fr); */ /* Base definition, overridden by specific sections */
  gap: var(--gap-20, 20px) var(--gap-30, 30px); /* row-gap column-gap */
}

.bid-thank-you-grid-item { /* Removed -full, as specific grids handle spans */
  display: flex;
  flex-direction: column;
  gap: var(--gap-5, 5px); /* Reduced gap for label-value pairs */
}

/* .bid-thank-you-grid-item-full {
  grid-column: span 2;
} */ /* Handled by specific grid layouts now */


.bid-thank-you-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.bid-thank-you-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

.bid-thank-you-divider {
  border: none;
  border-top: 1px solid var(--light-gray);
  margin-top: var(--m-10, 10px); /* Adjusted margin */
  margin-bottom: var(--m-10, 10px); /* Adjusted margin */
}

.customer-payment-section .customer-payment-flex-container {
  display: flex;
  gap: var(--gap-20, 20px); /* Gap between customer and payment sections */
}

.customer-details-container,
.payment-details-container {
  flex: 1; /* Each takes half the space */
  display: flex;
  flex-direction: column;
}

.customer-payment-divider {
  /* Styles for the divider between customer and payment details */
   align-self: stretch; /* Make divider full height of the flex container */
}

.bid-thank-you-customer-details {
  display: flex;
  flex-direction: column;
  gap: var(--gap-10, 10px);
}

.bid-thank-you-customer-details > div {
    display: flex;
    gap: var(--gap-5, 5px);
}

.bid-thank-you-payment-details {
  display: flex;
  align-items: center;
  gap: var(--gap-10, 10px);
}

.bid-thank-you-payment-icon {
  width: 30px; /* Approximate size */
  height: auto;
}

.bid-thank-you-item-info {
  display: flex;
  gap: var(--gap-20, 20px);
  align-items: flex-start; /* Align items to the top */
}

.bid-thank-you-item-image {
  width: 80px; /* Approximate size */
  height: auto;
  border-radius: var(--border-radius-medium);
  object-fit: cover;
}

.bid-thank-you-item-details {
  display: flex;
  flex-direction: column;
  gap: var(--gap-5, 5px);
}

.bid-thank-you-item-title {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
}

.bid-thank-you-item-author {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.bid-thank-you-actions {
  display: flex;
  justify-content: center;
  gap: var(--gap-20, 20px);
  margin-top: var(--m-30, 30px);
  width: 100%;
  max-width: 800px; /* Match card width */
}

/* Ensure buttons use existing styles but allow for specific overrides if needed */
.bid-thank-you-btn-outline {
  /* Uses .btn and .btn-outline from index.css */
  padding: 10px 20px; /* Example: Adjust padding if needed */
}

.bid-thank-you-btn-primary {
  /* Uses .btn and .btn-primary from index.css */
   padding: 10px 20px; /* Example: Adjust padding if needed */
}


/* Responsive adjustments */
@media (max-width: 768px) {
  .bid-thank-you-card {
    padding: var(--p-20, 20px);
  }

  .bid-information-section .bid-info-grid {
    grid-template-columns: 1fr; /* Stack bid info items */
    gap: var(--gap-15, 15px);
  }
  .bid-info-divider {
    display: none; /* Hide bid info divider on smaller screens */
  }

  .customer-payment-section .customer-payment-flex-container {
    flex-direction: column; /* Stack customer and payment details */
    gap: var(--gap-20, 20px);
  }

  .customer-payment-divider {
    display: none; /* Hide customer/payment divider on smaller screens */
  }
  .bid-thank-you-grid { /* General stacking for other grids if any */
    grid-template-columns: 1fr;
    gap: var(--gap-15, 15px);
  }

  /* .bid-thank-you-grid-item-full {
    grid-column: span 1;
  } */ /* Not needed if base grid is 1fr */

  .bid-thank-you-section-title {
    font-size: var(--heading6); /* Slightly smaller title for mobile */
  }

  .bid-thank-you-title {
    font-size: var(--heading4);
  }

  .bid-thank-you-actions {
    flex-direction: column; /* Stack buttons vertically */
    align-items: stretch; /* Make buttons full width */
  }

  .bid-thank-you-actions .btn {
    width: 100%;
    margin-bottom: var(--m-10, 10px);
  }
   .bid-thank-you-actions .btn:last-child {
    margin-bottom: 0;
  }

  .bid-thank-you-item-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  .bid-thank-you-item-image {
    margin-bottom: var(--m-10, 10px);
  }
}

@media (max-width: 480px) {
  .bid-thank-you-header-icon {
    width: 50px;
    height: 50px;
  }
  .bid-thank-you-title {
    font-size: var(--heading5);
  }
  .bid-thank-you-subtitle {
    font-size: var(--smallfont);
  }
  .bid-thank-you-card {
    padding: var(--p-15, 15px);
  }
   .bid-thank-you-section-title {
    font-size: var(--basefont);
  }
  .bid-thank-you-label,
  .bid-thank-you-value,
  .bid-thank-you-item-author {
    font-size: var(--smallfont);
  }
  .bid-thank-you-item-title {
    font-size: var(--basefont);
  }
}