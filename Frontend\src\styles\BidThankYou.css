.bid-thank-you-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--p-50) var(--p-20);
  background-color: var(--bg-gray);
  min-height: 100vh;
}

.bid-thank-you-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: var(--m-40);
}

.bid-thank-you-header-icon {
  width: 64px;
  height: 64px;
  margin-bottom: var(--m-20);
}

.bid-thank-you-title {
  font-size: var(--heading3);
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: var(--m-10);
}

.bid-thank-you-subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  max-width: 420px;
  line-height: 1.5;
}

.bid-thank-you-card {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  padding: var(--p-40);
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
}

.bid-thank-you-section {
  display: flex;
  flex-direction: column;
}

.bid-thank-you-section-title {
  font-size: var(--heading5);
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: var(--m-20);
}

/* Bid Information Grid Layout */
.bid-information-section .bid-info-grid {
  display: grid;
  grid-template-columns: 1fr 1px 1fr;
  grid-template-rows: 1fr 1fr;
  gap: var(--gap-20) var(--gap-30);
  align-items: start;
}

.bid-info-grid .bid-thank-you-grid-item:nth-child(1) {
  grid-column: 1;
  grid-row: 1;
}

.bid-info-grid .bid-thank-you-grid-item:nth-child(2) {
  grid-column: 3;
  grid-row: 1;
}

.bid-info-grid .bid-thank-you-grid-item:nth-child(3) {
  grid-column: 1;
  grid-row: 2;
}

.bid-info-grid .bid-thank-you-grid-item:nth-child(4) {
  grid-column: 3;
  grid-row: 2;
}

.bid-info-divider {
  grid-column: 2;
  grid-row: 1 / -1;
  width: 1px;
  background-color: var(--light-gray);
  justify-self: center;
}

.bid-thank-you-grid-item {
  display: flex;
  flex-direction: column;
  gap: var(--gap-5);
}

.bid-thank-you-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.bid-thank-you-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

.bid-thank-you-divider {
  border: none;
  border-top: 1px solid var(--light-gray);
  margin: var(--m-30) 0;
}

/* Customer and Payment Section */
.customer-payment-section .customer-payment-flex-container {
  display: flex;
  gap: var(--gap-40);
}

.customer-details-container,
.payment-details-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.customer-payment-divider {
  width: 1px;
  background-color: var(--light-gray);
  align-self: stretch;
}

.bid-thank-you-customer-details {
  display: flex;
  flex-direction: column;
  gap: var(--gap-15);
}

.bid-thank-you-customer-details > div {
  display: flex;
  flex-direction: column;
  gap: var(--gap-5);
}

.bid-thank-you-payment-details {
  display: flex;
  align-items: center;
  gap: var(--gap-10);
}

.bid-thank-you-payment-icon {
  width: 32px;
  height: auto;
}

/* Item Info Section */
.bid-thank-you-item-info {
  display: flex;
  gap: var(--gap-20);
  align-items: flex-start;
}

.bid-thank-you-item-image {
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius-medium);
  object-fit: cover;
  flex-shrink: 0;
}

.bid-thank-you-item-details {
  display: flex;
  flex-direction: column;
  gap: var(--gap-5);
  flex: 1;
}

.bid-thank-you-item-title {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
  line-height: 1.4;
}

.bid-thank-you-item-author {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Actions Section */
.bid-thank-you-actions {
  display: flex;
  justify-content: center;
  gap: var(--gap-20);
  margin-top: var(--m-40);
  width: 100%;
  max-width: 800px;
}

.bid-thank-you-btn-outline,
.bid-thank-you-btn-primary {
  padding: var(--p-15) var(--p-30);
  font-size: var(--basefont);
  font-weight: 500;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
}


/* Responsive Design */
@media (max-width: 768px) {
  .bid-thank-you-page {
    padding: var(--p-30) var(--p-15);
  }

  .bid-thank-you-card {
    padding: var(--p-30);
  }

  .bid-information-section .bid-info-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: var(--gap-20);
  }

  .bid-info-grid .bid-thank-you-grid-item:nth-child(1),
  .bid-info-grid .bid-thank-you-grid-item:nth-child(2),
  .bid-info-grid .bid-thank-you-grid-item:nth-child(3),
  .bid-info-grid .bid-thank-you-grid-item:nth-child(4) {
    grid-column: 1;
    grid-row: auto;
  }

  .bid-info-divider {
    display: none;
  }

  .customer-payment-section .customer-payment-flex-container {
    flex-direction: column;
    gap: var(--gap-30);
  }

  .customer-payment-divider {
    display: none;
  }

  .bid-thank-you-actions {
    flex-direction: column;
    gap: var(--gap-15);
  }

  .bid-thank-you-actions .btn {
    width: 100%;
  }

  .bid-thank-you-item-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .bid-thank-you-page {
    padding: var(--p-20) var(--p-10);
  }

  .bid-thank-you-header-icon {
    width: 50px;
    height: 50px;
  }

  .bid-thank-you-title {
    font-size: var(--heading5);
  }

  .bid-thank-you-subtitle {
    font-size: var(--smallfont);
  }

  .bid-thank-you-card {
    padding: var(--p-20);
  }

  .bid-thank-you-section-title {
    font-size: var(--basefont);
  }

  .bid-thank-you-label,
  .bid-thank-you-value,
  .bid-thank-you-item-author {
    font-size: var(--smallfont);
  }

  .bid-thank-you-item-title {
    font-size: var(--basefont);
  }

  .bid-thank-you-item-image {
    width: 60px;
    height: 60px;
  }
}